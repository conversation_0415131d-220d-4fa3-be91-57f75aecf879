import pandas as pd
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
import joblib  # for saving the model

# Load labeled data
df = pd.read_csv("ocr_keyword_counts.csv")

# Separate features and label
X = df.drop(columns=["filename", "label"])
y = df["label"]

# Split for evaluation (optional)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train Logistic Regression model
model = LogisticRegression(max_iter=1000)
model.fit(X_train, y_train)

# Evaluate on test set
y_pred = model.predict(X_test)
print("\nClassification Report:\n")
print(classification_report(y_test, y_pred))

# Save the model
joblib.dump(model, "invoice_classifier.pkl")

## Load unlabeled data
#unlabeled_df = pd.read_csv("unlabeled_data.csv")
#X_unlabeled = unlabeled_df.drop(columns=["filename"])
#
## Predict
#predictions = model.predict(X_unlabeled)
#
## Add predictions to the dataframe
#unlabeled_df["predicted_label"] = predictions  # 1 = invoice, 0 = not
#
## Save to CSV
#unlabeled_df.to_csv("predicted_labels.csv", index=False)
#print("Predictions saved to predicted_labels.csv")
#