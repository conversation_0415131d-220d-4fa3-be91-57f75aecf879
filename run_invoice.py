import os
import pytesseract
from pdf2image import convert_from_path
import pandas as pd
from collections import Counter

# Define keywords to search (lowercased for case-insensitive matching)
KEYWORDS = [
    "invoice", "bill", "payment", "amount", "total",
    "due date", "invoice number", "tax", "subtotal", 
    "credit memo", "credit notice", "credit note", "receipt",
    "statement", "email", "e-mail", "re:", "subject"
]
KEYWORDS = [kw.lower() for kw in KEYWORDS]

# Prepare DataFrame to hold results
records = []

# Iterate through all PDFs in current directory
for filename in os.listdir():
    if filename.lower().endswith(".pdf"):
        print(f"Processing {filename}...")

        try:
            # Convert PDF to images (one per page)
            pages = convert_from_path(filename, dpi=300)

            # Run OCR on all pages and combine text
            full_text = ""
            for page in pages:
                text = pytesseract.image_to_string(page)
                full_text += "\n" + text

            # Normalize text to lowercase for keyword matching
            full_text_lower = full_text.lower()

            # Count keyword occurrences
            keyword_counts = Counter()
            for kw in KEYWORDS:
                keyword_counts[kw] = full_text_lower.count(kw)

            # Create record
            record = {"filename": filename}
            record.update(keyword_counts)
            records.append(record)

        except Exception as e:
            print(f"Error processing {filename}: {e}")

# Convert to DataFrame
df = pd.DataFrame(records)

# Save to CSV
df.to_csv("ocr_keyword_counts.csv", index=False)
print("Keyword counts saved to ocr_keyword_counts.csv")
