#!/usr/bin/env python3
"""
Script to organize PDF files into folders based on invoice classification.
Reads the OCR keyword counts CSV file and moves PDFs into 'invoices' or 'non_invoices' folders.
"""

import pandas as pd
import os
import shutil
from pathlib import Path

def organize_pdfs_by_classification(csv_file='ocr_keyword_counts.csv', 
                                  source_dir='.', 
                                  invoice_folder='invoices', 
                                  non_invoice_folder='non_invoices'):
    """
    Organize PDF files into folders based on invoice classification.
    
    Parameters:
    csv_file (str): Path to the CSV file containing filename and label columns
    source_dir (str): Directory containing the PDF files
    invoice_folder (str): Name of folder for invoice PDFs
    non_invoice_folder (str): Name of folder for non-invoice PDFs
    """
    
    # Read the CSV file
    try:
        df = pd.read_csv(csv_file)
        print(f"Successfully loaded {csv_file}")
        print(f"Found {len(df)} records in the CSV file")
    except FileNotFoundError:
        print(f"Error: Could not find {csv_file}")
        return
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return
    
    # Check if required columns exist
    if 'filename' not in df.columns or 'label' not in df.columns:
        print("Error: CSV file must contain 'filename' and 'label' columns")
        return
    
    # Create target directories if they don't exist
    invoice_path = Path(source_dir) / invoice_folder
    non_invoice_path = Path(source_dir) / non_invoice_folder
    
    invoice_path.mkdir(exist_ok=True)
    non_invoice_path.mkdir(exist_ok=True)
    
    print(f"Created directories:")
    print(f"  - {invoice_path}")
    print(f"  - {non_invoice_path}")
    
    # Initialize counters
    moved_invoices = 0
    moved_non_invoices = 0
    missing_files = 0
    errors = 0
    
    # Process each file
    for index, row in df.iterrows():
        filename = row['filename']
        label = row['label']
        
        # Skip empty rows
        if pd.isna(filename) or pd.isna(label):
            continue
            
        source_file = Path(source_dir) / filename
        
        # Check if source file exists
        if not source_file.exists():
            print(f"Warning: File not found - {filename}")
            missing_files += 1
            continue
        
        try:
            # Determine destination based on label
            if label == 1:
                destination = invoice_path / filename
                moved_invoices += 1
                category = "invoice"
            elif label == 0:
                destination = non_invoice_path / filename
                moved_non_invoices += 1
                category = "non-invoice"
            else:
                print(f"Warning: Unknown label '{label}' for file {filename}")
                continue
            
            # Move the file
            shutil.move(str(source_file), str(destination))
            print(f"Moved {filename} to {category} folder")
            
        except Exception as e:
            print(f"Error moving {filename}: {e}")
            errors += 1
    
    # Print summary
    print("\n" + "="*50)
    print("ORGANIZATION SUMMARY")
    print("="*50)
    print(f"Total files processed: {len(df)}")
    print(f"Invoices moved: {moved_invoices}")
    print(f"Non-invoices moved: {moved_non_invoices}")
    print(f"Missing files: {missing_files}")
    print(f"Errors: {errors}")
    print(f"Successfully moved: {moved_invoices + moved_non_invoices}")
    
    # Show distribution
    print(f"\nDistribution:")
    print(f"  - Invoices: {moved_invoices} files")
    print(f"  - Non-invoices: {moved_non_invoices} files")

def preview_organization(csv_file='ocr_keyword_counts.csv'):
    """
    Preview what the organization would look like without actually moving files.
    """
    try:
        df = pd.read_csv(csv_file)
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return
    
    if 'filename' not in df.columns or 'label' not in df.columns:
        print("Error: CSV file must contain 'filename' and 'label' columns")
        return
    
    # Count by label
    label_counts = df['label'].value_counts().sort_index()
    
    print("PREVIEW: File organization by label")
    print("="*40)
    for label, count in label_counts.items():
        category = "invoices" if label == 1 else "non-invoices" if label == 0 else "unknown"
        print(f"Label {label} ({category}): {count} files")
    
    print(f"\nTotal files: {len(df)}")
    
    # Show some examples
    print(f"\nExample files by category:")
    for label in sorted(df['label'].unique()):
        if pd.notna(label):
            category = "invoices" if label == 1 else "non-invoices" if label == 0 else "unknown"
            examples = df[df['label'] == label]['filename'].head(3).tolist()
            print(f"  {category} (label {label}): {examples}")

if __name__ == "__main__":
    print("PDF Organization Script")
    print("="*30)
    
    # First show a preview
    print("1. PREVIEW MODE")
    preview_organization()
    
    # Ask for confirmation
    print(f"\n2. ORGANIZATION MODE")
    response = input("Do you want to proceed with organizing the files? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        organize_pdfs_by_classification()
    else:
        print("Organization cancelled.")
